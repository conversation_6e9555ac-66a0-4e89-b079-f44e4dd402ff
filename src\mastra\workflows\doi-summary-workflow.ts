import { createWorkflow, createStep } from '@mastra/core/workflows';
import { z } from 'zod';
import { articleSummaryTool } from '../tools/article-summary-tool';

const extractArticleData = createStep({
  id: 'extract-article-data',
  description: 'DO<PERSON> ile makale verileri<PERSON> topla (sadece gerçek veriler)',
  inputSchema: z.object({
    doi: z.string().describe('DOI numarası (örn: 10.1016/j.kint.2021.02.040)'),
  }),
  outputSchema: z.object({
    title: z.string(),
    authors: z.string(),
    journal: z.string(),
    year: z.string(),
    field: z.string(),
    abstract: z.string(),
    findings: z.string(),
    conclusion: z.string(),
    methodology: z.string(),
    methods: z.string(),
    dataQuality: z.string(),
    keywords: z.string(),
    citationCount: z.number().optional(),
    pmid: z.string().optional(),
  }),
  execute: async ({ inputData, mastra }) => {
    if (!inputData?.doi) {
      throw new Error('Geçerli bir DOI parametresi gerekli');
    }


    // Doğrudan import edilen articleSummaryTool kullanılıyor
    // @ts-ignore
    const meta = await articleSummaryTool.execute({ context: { doi: inputData.doi } });

    return {
      title: meta.title,
      authors: meta.authors,
      journal: meta.journal,
      year: meta.year,
      field: 'Genel', // Henüz sınıflandırma yapılmıyor
      abstract: meta.abstract,
      findings: '',
      conclusion: '',
      methodology: '',
      methods: '',
      dataQuality: meta.abstract && meta.keywords ? 'FULL' : 'PARTIAL',
      keywords: meta.keywords || '',
      citationCount: meta.citationCount || 0,
      pmid: meta.pmid || '',
    };
  },
});

export const doiSummaryWorkflow = createWorkflow({
  id: 'doi-summary-workflow',
  inputSchema: z.object({ doi: z.string() }),
  outputSchema: extractArticleData.outputSchema,
})
  .then(extractArticleData)
  .commit();
